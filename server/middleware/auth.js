/**
 * Authentication middleware for protecting sensitive API endpoints
 * In a production environment, this should be replaced with proper JWT authentication
 */
export function authenticateToken(req, res, next) {
    // For development, we'll use a simple API key approach
    // In production, use proper JWT tokens or OAuth
    
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    
    // In a real implementation, verify the token against a database or JWT verification
    // For now, we'll use a simple check
    if (token !== process.env.API_AUTH_TOKEN || !process.env.API_AUTH_TOKEN) {
        return res.status(403).json({ error: 'Invalid or expired token' });
    }
    
    // Add user info to request for use in routes
    req.user = {
        id: 'user-id-from-token',
        role: 'developer' // In real implementation, this would come from token verification
    };
    
    next();
}

/**
 * Authorization middleware for admin-only operations
 */
export function authorizeAdmin(req, res, next) {
    // Check if user has admin role
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        return res.status(403).json({ error: 'Admin access required' });
    }
}

/**
 * Simple authentication for development
 * In production, replace with proper authentication system
 */
export function authenticateDevelopment(req, res, next) {
    // For development, we allow access with a development token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    // Allow access with development token or no token in development mode
    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
        // If they provide the correct API token, give them admin access
        if (token === process.env.API_AUTH_TOKEN && process.env.API_AUTH_TOKEN) {
            req.user = { id: 'dev-admin', role: 'admin' };
        } else {
            req.user = { id: 'dev-user', role: 'developer' };
        }
        next();
        return;
    }

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    if (token !== process.env.API_AUTH_TOKEN || !process.env.API_AUTH_TOKEN) {
        return res.status(403).json({ error: 'Invalid or expired token' });
    }

    req.user = { id: 'authenticated-user', role: 'admin' };
    next();
}