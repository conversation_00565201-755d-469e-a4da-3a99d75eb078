#!/usr/bin/env node

/**
 * Tokenomics Stress Test Runner
 * 
 * Main entry point for running comprehensive tokenomics stress tests.
 * This script validates treasury sustainability and creator reward distribution
 * through real user behavior simulation on the local Hardhat blockchain.
 * 
 * Usage:
 *   node test/tokenomics/runStressTest.js [options]
 * 
 * Options:
 *   --duration <ms>     Test duration in milliseconds (default: 300000 = 5 minutes)
 *   --users <number>    Maximum concurrent users (default: 10)
 *   --report-file <path> Save detailed report to file
 *   --verbose           Enable verbose logging
 *   --scenario <name>   Run specific scenario only (grinder|creator|coordination|drain)
 */

import { TokenomicsStressTest } from './TokenomicsStressTest.js';
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';

class StressTestRunner {
    constructor() {
        this.config = this.parseArguments();
        this.stressTest = null;
        this.startTime = null;
        this.endTime = null;

        // Handle process signals to prevent EPIPE errors
        this.setupSignalHandlers();
    }

    /**
     * Setup signal handlers to prevent EPIPE errors
     */
    setupSignalHandlers() {
        // Handle SIGPIPE to prevent EPIPE errors
        process.on('SIGPIPE', () => {
            // Silently ignore SIGPIPE signals
        });

        // Handle stdout/stderr errors
        process.stdout.on('error', (error) => {
            if (error.code === 'EPIPE') {
                // Silently ignore EPIPE errors on stdout
                return;
            }
            console.error('stdout error:', error);
        });

        process.stderr.on('error', (error) => {
            if (error.code === 'EPIPE') {
                // Silently ignore EPIPE errors on stderr
                return;
            }
            console.error('stderr error:', error);
        });

        // Handle uncaught exceptions related to EPIPE
        process.on('uncaughtException', (error) => {
            if (error.code === 'EPIPE') {
                // Silently ignore EPIPE errors
                return;
            }
            console.error('Uncaught exception:', error);
            process.exit(1);
        });
    }

    /**
     * Parse command line arguments
     */
    parseArguments() {
        const args = process.argv.slice(2);
        const config = {
            apiBaseUrl: 'http://localhost:3001/api',
            hardhatUrl: 'http://localhost:8545',
            chainId: 31337,
            hotWalletAddress: '******************************************',
            testDuration: 300000, // 5 minutes
            maxConcurrentUsers: 10,
            verbose: false,
            reportFile: null,
            scenario: null
        };

        for (let i = 0; i < args.length; i++) {
            switch (args[i]) {
                case '--duration':
                    config.testDuration = parseInt(args[++i]) || config.testDuration;
                    break;
                case '--users':
                    config.maxConcurrentUsers = parseInt(args[++i]) || config.maxConcurrentUsers;
                    break;
                case '--report-file':
                    config.reportFile = args[++i];
                    break;
                case '--verbose':
                    config.verbose = true;
                    break;
                case '--scenario':
                    config.scenario = args[++i];
                    break;
                case '--help':
                    this.showHelp();
                    process.exit(0);
                    break;
            }
        }

        return config;
    }

    /**
     * Show help information
     */
    showHelp() {
        console.log(`
🧪 WarpSector Tokenomics Stress Test Runner

DESCRIPTION:
  Comprehensive stress testing framework for validating treasury sustainability
  and creator reward distribution through real user behavior simulation.

USAGE:
  node test/tokenomics/runStressTest.js [options]

OPTIONS:
  --duration <ms>       Test duration in milliseconds (default: 300000 = 5 minutes)
  --users <number>      Maximum concurrent users (default: 10)
  --report-file <path>  Save detailed report to file
  --verbose             Enable verbose logging
  --scenario <name>     Run specific scenario only:
                        - grinder: Sequential grinding test
                        - creator: Creator reward distribution test
                        - coordination: Multi-account coordination test
                        - drain: Treasury drain test
  --help               Show this help message

EXAMPLES:
  # Run full stress test suite
  node test/tokenomics/runStressTest.js

  # Run 10-minute test with detailed report
  node test/tokenomics/runStressTest.js --duration 600000 --report-file stress-report.json

  # Run only creator reward test
  node test/tokenomics/runStressTest.js --scenario creator --verbose

REQUIREMENTS:
  - Hardhat local node running on localhost:8545 (Chain ID 31337)
  - Game server running on localhost:3001
  - Hot wallet configured with test ETH
  - ETH Test Mode enabled in game

For more information, see: LOCAL_TESTING_GUIDE.md
        `);
    }

    /**
     * Run the stress test suite
     */
    async run() {
        try {
            console.log('🚀 Starting WarpSector Tokenomics Stress Test');
            console.log('=' .repeat(60));
            
            this.printConfiguration();
            await this.validatePrerequisites();
            
            // Initialize stress test framework
            this.stressTest = new TokenomicsStressTest(this.config);
            await this.stressTest.initialize();
            
            this.startTime = Date.now();
            
            // Run tests based on scenario selection
            let report;
            if (this.config.scenario) {
                report = await this.runSpecificScenario(this.config.scenario);
            } else {
                report = await this.stressTest.runStressTest();
            }
            
            this.endTime = Date.now();
            
            // Process and display results
            await this.processResults(report);
            
            console.log('✅ Stress test completed successfully');
            
        } catch (error) {
            console.error('❌ Stress test failed:', error);
            process.exit(1);
        }
    }

    /**
     * Print test configuration
     */
    printConfiguration() {
        console.log('📋 Test Configuration:');
        console.log(`   Duration: ${this.config.testDuration / 1000} seconds`);
        console.log(`   Max Users: ${this.config.maxConcurrentUsers}`);
        console.log(`   API URL: ${this.config.apiBaseUrl}`);
        console.log(`   Hardhat URL: ${this.config.hardhatUrl}`);
        console.log(`   Hot Wallet: ${this.config.hotWalletAddress}`);
        console.log(`   Scenario: ${this.config.scenario || 'All scenarios'}`);
        console.log(`   Verbose: ${this.config.verbose ? 'Enabled' : 'Disabled'}`);
        console.log('');
    }

    /**
     * Validate prerequisites before running tests
     */
    async validatePrerequisites() {
        console.log('🔍 Validating prerequisites...');
        
        const checks = [
            { name: 'Game Server', check: () => this.checkGameServer() },
            { name: 'Hardhat Node', check: () => this.checkHardhatNode() },
            { name: 'Hot Wallet Balance', check: () => this.checkHotWalletBalance() },
            { name: 'Test Accounts', check: () => this.checkTestAccounts() }
        ];

        for (const { name, check } of checks) {
            try {
                await check();
                console.log(`   ✅ ${name}: OK`);
            } catch (error) {
                console.error(`   ❌ ${name}: ${error.message}`);
                throw new Error(`Prerequisite check failed: ${name}`);
            }
        }
        
        console.log('✅ All prerequisites validated');
        console.log('');
    }

    /**
     * Check if game server is running
     */
    async checkGameServer() {
        const response = await fetch(`${this.config.apiBaseUrl}/health`);
        if (!response.ok) {
            throw new Error(`Server not responding (${response.status})`);
        }
    }

    /**
     * Check if Hardhat node is running
     */
    async checkHardhatNode() {
        const response = await fetch(this.config.hardhatUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'eth_chainId',
                params: [],
                id: 1
            })
        });

        const result = await response.json();
        if (result.error) {
            throw new Error(result.error.message);
        }

        const chainId = parseInt(result.result, 16);
        if (chainId !== this.config.chainId) {
            throw new Error(`Wrong chain ID: expected ${this.config.chainId}, got ${chainId}`);
        }
    }

    /**
     * Check hot wallet balance
     */
    async checkHotWalletBalance() {
        const response = await fetch(`${this.config.apiBaseUrl}/wallet/balance`, {
            headers: { 'Authorization': 'Bearer test-token' }
        });

        if (!response.ok) {
            throw new Error(`Cannot check hot wallet balance (${response.status})`);
        }

        const result = await response.json();
        const balance = parseFloat(result.balance);
        
        if (balance < 1.0) {
            throw new Error(`Hot wallet balance too low: ${balance} ETH`);
        }
    }

    /**
     * Check test accounts availability
     */
    async checkTestAccounts() {
        const response = await fetch(this.config.hardhatUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'eth_accounts',
                params: [],
                id: 1
            })
        });

        const result = await response.json();
        if (result.error) {
            throw new Error(result.error.message);
        }

        if (result.result.length < this.config.maxConcurrentUsers) {
            throw new Error(`Not enough test accounts: need ${this.config.maxConcurrentUsers}, got ${result.result.length}`);
        }
    }

    /**
     * Run specific test scenario
     */
    async runSpecificScenario(scenario) {
        console.log(`🎯 Running specific scenario: ${scenario}`);
        
        switch (scenario) {
            case 'grinder':
                await this.stressTest.runSequentialGrindingTest();
                break;
            case 'creator':
                await this.stressTest.runCreatorRewardTest();
                break;
            case 'coordination':
                await this.stressTest.runMultiAccountCoordinationTest();
                break;
            case 'drain':
                await this.stressTest.runTreasuryDrainTest();
                break;
            default:
                throw new Error(`Unknown scenario: ${scenario}`);
        }

        return await this.stressTest.generateReport();
    }

    /**
     * Process and display test results
     */
    async processResults(report) {
        console.log('');
        console.log('📊 STRESS TEST RESULTS');
        console.log('=' .repeat(60));
        
        this.displayTestSummary(report);
        this.displayTreasuryAnalysis(report);
        this.displayCreatorRewardAnalysis(report);
        this.displaySustainabilityAssessment(report);
        this.displayRecommendations(report);
        
        // Save detailed report if requested
        if (this.config.reportFile) {
            await this.saveDetailedReport(report);
        }
    }

    /**
     * Display test summary
     */
    displayTestSummary(report) {
        const summary = report.testSummary;
        const duration = this.endTime - this.startTime;
        
        console.log('📋 Test Summary:');
        console.log(`   Total Duration: ${duration / 1000} seconds`);
        console.log(`   Users Simulated: ${summary.totalUsers}`);
        console.log(`   Total Transactions: ${summary.totalTransactions}`);
        console.log(`   Test Completion: ${duration < this.config.testDuration ? 'Early completion' : 'Full duration'}`);
        console.log('');
    }

    /**
     * Display treasury analysis
     */
    displayTreasuryAnalysis(report) {
        const treasury = report.treasuryAnalysis;
        
        console.log('💰 Treasury Analysis:');
        console.log(`   Initial Balance: ${treasury.treasuryStatus?.initialBalance || 'N/A'} ETH`);
        console.log(`   Final Balance: ${treasury.treasuryStatus?.currentBalance || 'N/A'} ETH`);
        console.log(`   Balance Change: ${treasury.treasuryStatus?.balanceChange || 'N/A'} ETH`);
        console.log(`   Net Flow: ${treasury.treasuryStatus?.netFlow || 'N/A'} ETH`);
        console.log(`   Risk Level: ${treasury.treasuryStatus?.riskLevel || 'Unknown'}`);
        console.log('');
    }

    /**
     * Display creator reward analysis
     */
    displayCreatorRewardAnalysis(report) {
        const creator = report.creatorRewardAnalysis;
        
        console.log('🎨 Creator Reward Analysis:');
        console.log(`   Total Creator Rewards: ${creator.totalCreatorRewards || 0} ETH`);
        console.log(`   Average Reward per Environment: ${creator.averageRewardPerEnvironment || 0} ETH`);
        console.log(`   Reward Distribution Accuracy: ${creator.rewardDistributionAccuracy || 0}%`);
        console.log('');
    }

    /**
     * Display sustainability assessment
     */
    displaySustainabilityAssessment(report) {
        const sustainability = report.sustainabilityAssessment;
        
        console.log('📈 Sustainability Assessment:');
        console.log(`   Is Sustainable: ${sustainability.isSustainable ? '✅ YES' : '❌ NO'}`);
        console.log(`   Risk Level: ${sustainability.riskLevel}`);
        console.log(`   Projected Runtime: ${sustainability.projectedRuntime === Infinity ? 'Infinite' : `${sustainability.projectedRuntime} seconds`}`);
        console.log('');
    }

    /**
     * Display recommendations
     */
    displayRecommendations(report) {
        console.log('💡 Recommendations:');
        report.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
        console.log('');
    }

    /**
     * Save detailed report to file
     */
    async saveDetailedReport(report) {
        try {
            const reportData = {
                ...report,
                testConfiguration: this.config,
                executionTime: {
                    startTime: this.startTime,
                    endTime: this.endTime,
                    duration: this.endTime - this.startTime
                },
                generatedAt: new Date().toISOString()
            };

            const reportPath = path.resolve(this.config.reportFile);
            await fs.promises.writeFile(reportPath, JSON.stringify(reportData, null, 2));
            
            console.log(`📄 Detailed report saved to: ${reportPath}`);
        } catch (error) {
            console.error(`❌ Failed to save report: ${error.message}`);
        }
    }
}

// Run the stress test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const runner = new StressTestRunner();
    runner.run().catch(error => {
        console.error('❌ Stress test runner failed:', error);
        process.exit(1);
    });
}

export { StressTestRunner };
