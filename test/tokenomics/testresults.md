npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 10000.0 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: 0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc
👤 Created creator user simulator: creator_1 (0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc)
✅ Initialized daily reward tracker for user: 0x976ea74026e726554db657fa54763abd0c3a0aa9
👤 Created creator user simulator: creator_2 (0x976ea74026e726554db657fa54763abd0c3a0aa9)
✅ Initialized daily reward tracker for user: 0x14dc79964da2c08b23698b3d3cc7ca32193d9955
👤 Created casual user simulator: casual_1 (0x14dc79964da2c08b23698b3d3cc7ca32193d9955)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
💰 Initial treasury balance: 10000 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Ancient temple ruins in a jungle"
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated
💰 Initial treasury balance: 10000 ETH
🎯 Starting grinder session: grinder_1
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated
💰 Pre-stress treasury balance: 10000 ETH
⚡ Initiating maximum stress scenario...
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Ice planet with aurora borealis"
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated
⚡ grinder_3 starting maximum stress behavior
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated
⚡ grinder_1 starting maximum stress behavior
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated
⚡ casual_1 starting maximum stress behavior
✅ API Call: POST /wallet/send (506ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xab36748b8b8208c3ec3adac1786759f029dd632964f1649c8899e7171bc9769a
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0xab36748b8b8208c3ec3adac1786759f029dd632964f1649c8899e7171bc9769a
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
✅ API Call: POST /wallet/send (505ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x457260af78056ca03a85130880c7e92b229a5834da7c63ee1be0bca4dec93c3f
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0x457260af78056ca03a85130880c7e92b229a5834da7c63ee1be0bca4dec93c3f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
✅ API Call: POST /wallet/send (337ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x03a03e0ee0b2439630aeb9fc565c1abde844aa20a650d1d13b66ba760e94dc47
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0x03a03e0ee0b2439630aeb9fc565c1abde844aa20a650d1d13b66ba760e94dc47
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
⚡ casual_3 starting maximum stress behavior
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 2500 ETH
💸 whale_1 spending 2500 ETH for: Reality Warp
💸 Token Spend Tracked: 2500 tokens from whale_1
💸 whale_1 completed spend transaction for: Reality Warp
✅ API Call: POST /wallet/send (381ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x9561f9a198dc7c5263b69adddb7db446d8156d2fcbd29eec650ace22e979412d
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0x9561f9a198dc7c5263b69adddb7db446d8156d2fcbd29eec650ace22e979412d
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
⚡ whale_1 starting maximum stress behavior
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 2500 ETH
💸 whale_1 spending 2500 ETH for: Reality Warp
💸 Token Spend Tracked: 2500 tokens from whale_1
💸 whale_1 completed spend transaction for: Reality Warp
⚡ grinder_2 starting maximum stress behavior
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 2500 ETH
💸 whale_2 spending 2500 ETH for: Reality Warp
💸 Token Spend Tracked: 2500 tokens from whale_2
💸 whale_2 completed spend transaction for: Reality Warp
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 1500 ETH
💸 whale_1 spending 1500 ETH for: Extra Life
💸 Token Spend Tracked: 1500 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Life
✅ API Call: POST /wallet/send (539ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xbd8fbcf7d6a4fd1d48b925de2bca50de3c47f5abdfbcd07204da7cc2be70e9e3
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0xbd8fbcf7d6a4fd1d48b925de2bca50de3c47f5abdfbcd07204da7cc2be70e9e3
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
⚡ casual_2 starting maximum stress behavior
✅ API Call: POST /wallet/send (469ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xac2035aab14b71fc0debc8b215c7a1a869b8e9fc30d86b3ee51965324c78e81f
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xac2035aab14b71fc0debc8b215c7a1a869b8e9fc30d86b3ee51965324c78e81f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 1500 ETH
💸 whale_1 spending 1500 ETH for: Extra Life
💸 Token Spend Tracked: 1500 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Life
⚡ whale_2 starting maximum stress behavior
🛒 Reality Warp: 25000 WISH → 2500 WISH (90% off) → 2500 ETH
💸 whale_2 spending 2500 ETH for: Reality Warp
💸 Token Spend Tracked: 2500 tokens from whale_2
💸 whale_2 completed spend transaction for: Reality Warp
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 1500 ETH
💸 whale_2 spending 1500 ETH for: Extra Life
💸 Token Spend Tracked: 1500 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Life
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 1000 ETH
💸 whale_1 spending 1000 ETH for: Extra Wingman
💸 Token Spend Tracked: 1000 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Wingman
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Ice planet with aurora borealis"
🎮 Level 2: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 2 completion - 100% enemies defeated
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 2: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 1000 ETH
💸 whale_1 spending 1000 ETH for: Extra Wingman
💸 Token Spend Tracked: 1000 tokens from whale_1
💸 whale_1 completed spend transaction for: Extra Wingman
🎮 Level 2: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated
🛒 Extra Life: 15000 WISH → 1500 WISH (90% off) → 1500 ETH
💸 whale_2 spending 1500 ETH for: Extra Life
💸 Token Spend Tracked: 1500 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Life
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 1000 ETH
💸 whale_2 spending 1000 ETH for: Extra Wingman
💸 Token Spend Tracked: 1000 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Wingman
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 750 ETH
💸 whale_1 spending 750 ETH for: Spread Ammo
💸 Token Spend Tracked: 750 tokens from whale_1
💸 whale_1 completed spend transaction for: Spread Ammo
✅ API Call: POST /wallet/send (455ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x9128c4f7d16b4d434e5aee9afba5924bf630936444c4ff78d9b187b0c898bc53
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0x9128c4f7d16b4d434e5aee9afba5924bf630936444c4ff78d9b187b0c898bc53
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 750 ETH
💸 whale_1 spending 750 ETH for: Spread Ammo
💸 Token Spend Tracked: 750 tokens from whale_1
💸 whale_1 completed spend transaction for: Spread Ammo
🚫 Level 2: No reward (level_already_completed_today)
🛒 Extra Wingman: 10000 WISH → 1000 WISH (90% off) → 1000 ETH
💸 whale_2 spending 1000 ETH for: Extra Wingman
💸 Token Spend Tracked: 1000 tokens from whale_2
💸 whale_2 completed spend transaction for: Extra Wingman
🚫 Level 2: No reward (level_already_completed_today)
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 750 ETH
💸 whale_2 spending 750 ETH for: Spread Ammo
💸 Token Spend Tracked: 750 tokens from whale_2
💸 whale_2 completed spend transaction for: Spread Ammo
🎁 Awarding 375 ETH to whale_1 for: Level 1 completion - minimal effort
✅ API Call: POST /wallet/send (434ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xc6ba58c4d7b68a4ede59857bde53e87c72e543525ee6b0d4a744f5eb42532191
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0xc6ba58c4d7b68a4ede59857bde53e87c72e543525ee6b0d4a744f5eb42532191
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
✅ API Call: POST /wallet/send (423ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xfa14fcc01540981116820069e6a53382f1a29a75d4a8c67a5fe45a067fee5ae6
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xfa14fcc01540981116820069e6a53382f1a29a75d4a8c67a5fe45a067fee5ae6
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
✅ API Call: POST /wallet/send (343ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xcc711e4840e0a19b9f4039c1fe06dfbdbfb77e03e3aa5de5b31047c2198b7242
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xcc711e4840e0a19b9f4039c1fe06dfbdbfb77e03e3aa5de5b31047c2198b7242
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎮 Level 3: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 3 completion - 100% enemies defeated
🛒 Spread Ammo: 7500 WISH → 750 WISH (90% off) → 750 ETH
💸 whale_2 spending 750 ETH for: Spread Ammo
💸 Token Spend Tracked: 750 tokens from whale_2
💸 whale_2 completed spend transaction for: Spread Ammo
🎁 Awarding 375 ETH to whale_2 for: Level 1 completion - minimal effort
✅ API Call: POST /wallet/send (287ms)
💰 ETH Transfer Tracked: 375 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 375 ETH to ******************************************
📤 Transaction hash: 0x5593fd5702456d3440b8d6be816ebf68e9e95e26b8b8bb7ee03bc27dcc75f8a1
📉 Treasury outflow recorded: -375.000000 ETH (Reward to whale_1: Level 1 completion - minimal effort)
🔍 Tracking token award: whale_1, 375 ETH, 0x5593fd5702456d3440b8d6be816ebf68e9e95e26b8b8bb7ee03bc27dcc75f8a1
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_1, amount=375
🎁 Token Award Tracked: 375 tokens to whale_1
🚫 Level 2: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (190ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xa460a45f79d9642861b4aac90462f9666b0a0af5f51a5b515dce0ae325b2c05c
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0xa460a45f79d9642861b4aac90462f9666b0a0af5f51a5b515dce0ae325b2c05c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
✅ API Call: POST /wallet/send (168ms)
💰 ETH Transfer Tracked: 375 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 375 ETH to ******************************************
📤 Transaction hash: 0xd09bd640b0c087fa020cffcb7eb926fcce428c6bbbec620706a53c4c8d55bb7b
📉 Treasury outflow recorded: -375.000000 ETH (Reward to whale_2: Level 1 completion - minimal effort)
🔍 Tracking token award: whale_2, 375 ETH, 0xd09bd640b0c087fa020cffcb7eb926fcce428c6bbbec620706a53c4c8d55bb7b
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_2, amount=375
🎁 Token Award Tracked: 375 tokens to whale_2
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 3: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 3: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated
🚫 Level 3: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (184ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x18e7a52fca41345362fb1cbe0ffc6f3be56d067c1496f8b990449c39c174c146
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0x18e7a52fca41345362fb1cbe0ffc6f3be56d067c1496f8b990449c39c174c146
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
✅ API Call: POST /wallet/send (143ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xd66750693ce0b5db50060311d68594d4447f348a41dc2d46ecb82a7b5d84587f
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xd66750693ce0b5db50060311d68594d4447f348a41dc2d46ecb82a7b5d84587f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 4: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 4 completion - 100% enemies defeated
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (173ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xc92822891b40436f72516abd1461666c3c82fdd7fbe635cc78688aa3feec40fe
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0xc92822891b40436f72516abd1461666c3c82fdd7fbe635cc78688aa3feec40fe
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 4: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 4: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x95bbcd3fdbdadeeb5ec0ddc7d478339b4ba3a9e3e109d25e590ddfe8b73e0b3a
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0x95bbcd3fdbdadeeb5ec0ddc7d478339b4ba3a9e3e109d25e590ddfe8b73e0b3a
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎁 Awarding 750 ETH to whale_1 for: Level 2 completion - minimal effort
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (154ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xde263da1a00ff6d68d54a317d612de80eaa4739185ff059076950d4c199f7b64
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xde263da1a00ff6d68d54a317d612de80eaa4739185ff059076950d4c199f7b64
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎁 Awarding 750 ETH to whale_2 for: Level 2 completion - minimal effort
✅ API Call: POST /wallet/send (143ms)
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 750 ETH to ******************************************
📤 Transaction hash: 0x0d40d792c54883bd7c0eb940cbafcb4565deb8be062c8815d238a66ff3f99f0a
📉 Treasury outflow recorded: -750.000000 ETH (Reward to whale_1: Level 2 completion - minimal effort)
🔍 Tracking token award: whale_1, 750 ETH, 0x0d40d792c54883bd7c0eb940cbafcb4565deb8be062c8815d238a66ff3f99f0a
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_1, amount=750
🎁 Token Award Tracked: 750 tokens to whale_1
🎮 Level 5: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_1 for: Level 5 completion - 100% enemies defeated
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (131ms)
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 750 ETH to ******************************************
📤 Transaction hash: 0x4daccf1be9f8f4dbfac133f62680d018ce9319b145d4e7438b9c10561d25c7a8
📉 Treasury outflow recorded: -750.000000 ETH (Reward to whale_2: Level 2 completion - minimal effort)
🔍 Tracking token award: whale_2, 750 ETH, 0x4daccf1be9f8f4dbfac133f62680d018ce9319b145d4e7438b9c10561d25c7a8
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=whale_2, amount=750
🎁 Token Award Tracked: 750 tokens to whale_2
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (139ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x092e2f26c1b31d82c078e60342d3329ac81fb851e064ef1f725a58a8616ea8c9
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_1: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 125 ETH, 0x092e2f26c1b31d82c078e60342d3329ac81fb851e064ef1f725a58a8616ea8c9
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_1
🎮 Level 5: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (148ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xb7a574bf72389d2bebec8ae22a4ad418eb5ccb1c3a9ca1494b97e847aef9861f
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xb7a574bf72389d2bebec8ae22a4ad418eb5ccb1c3a9ca1494b97e847aef9861f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎮 Level 5: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (136ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xa6e3b9c6ebf1f9ad0f4cda69251337e015602ae98728ea739d41fdadccbfd12e
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xa6e3b9c6ebf1f9ad0f4cda69251337e015602ae98728ea739d41fdadccbfd12e
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎮 Level 6: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_1 for: Level 6 completion - 100% enemies defeated
🚫 Level 6: No reward (level_already_completed_today)
🚫 Level 6: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (152ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0xb0c3cac7d677750015636e553a54b78020c74b4d71adb8b7e4a4f872c0051089
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_1: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 200 ETH, 0xb0c3cac7d677750015636e553a54b78020c74b4d71adb8b7e4a4f872c0051089
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_1
🎮 Level 6: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated
🚫 Level 6: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (133ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x87d57e640913ca0e84c14f2a4ca39b3e0e595238436699fdb70f4d009beea632
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_3: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 200 ETH, 0x87d57e640913ca0e84c14f2a4ca39b3e0e595238436699fdb70f4d009beea632
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_3
🎮 Level 6: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🚫 Level 6: No reward (level_already_completed_today)
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
✅ API Call: POST /wallet/send (151ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0xe229e2f67e9a2a368e8809da6d8955efb9d0763c6bdb482355c32fa66c9f767f
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0xe229e2f67e9a2a368e8809da6d8955efb9d0763c6bdb482355c32fa66c9f767f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🎮 Level 7: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_1 for: Level 7 completion - 100% enemies defeated
🚫 Level 7: No reward (level_already_completed_today)
🚫 Level 7: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (139ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x8b8be9e7ad7d5859c0a33f93110c2d3dd6468fda8927feca88932c7276662c07
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_1: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 200 ETH, 0x8b8be9e7ad7d5859c0a33f93110c2d3dd6468fda8927feca88932c7276662c07
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_1
🎮 Level 7: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 7 completion - 100% enemies defeated
🚫 Level 7: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (153ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x0b14b8ee1f30ee8cd1e248d01ad1ec365c1f1f5a6eebe858c32a622878befc5c
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_3: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 200 ETH, 0x0b14b8ee1f30ee8cd1e248d01ad1ec365c1f1f5a6eebe858c32a622878befc5c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_3
🎮 Level 7: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 7 completion - 100% enemies defeated
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 8: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_1 for: Level 8 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (137ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x65351905a2a6942f8714563b3f542b4a9ac1673923ab8969de0495d4b15c5e0e
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x65351905a2a6942f8714563b3f542b4a9ac1673923ab8969de0495d4b15c5e0e
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🚫 Level 8: No reward (level_already_completed_today)
🚫 Level 8: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (159ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0xa6d38eefbddf3655937cba8c7cc815005c7f9af1275e7978d544c9ae4aa7dd67
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_1: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 200 ETH, 0xa6d38eefbddf3655937cba8c7cc815005c7f9af1275e7978d544c9ae4aa7dd67
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_1
🎮 Level 8: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 8 completion - 100% enemies defeated
🚫 Level 8: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (151ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x840a54cdb843fdf78fcb49bc5d64a3e1be2f078a7d05f4e42074af528487f815
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_3: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 200 ETH, 0x840a54cdb843fdf78fcb49bc5d64a3e1be2f078a7d05f4e42074af528487f815
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_3
🎮 Level 8: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 8 completion - 100% enemies defeated
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 9: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_1 for: Level 9 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (137ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x98fe57b788e6cfddc57d64b10a777bd5208b7cba2598d92f5e35b31239c29126
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x98fe57b788e6cfddc57d64b10a777bd5208b7cba2598d92f5e35b31239c29126
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🚫 Level 9: No reward (level_already_completed_today)
🚫 Level 9: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (144ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x984eab3f88ae6381a9fab5d8f3e45778e3f793ebdad6d6d6adcf33f09bf668fc
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_1: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 200 ETH, 0x984eab3f88ae6381a9fab5d8f3e45778e3f793ebdad6d6d6adcf33f09bf668fc
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_1
🎮 Level 9: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 9 completion - 100% enemies defeated
🚫 Level 9: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (150ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x6c1ff05a11cc53cbb84643eba6c95d30b314feba4d2135e82e9367b7d257e07c
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_3: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 200 ETH, 0x6c1ff05a11cc53cbb84643eba6c95d30b314feba4d2135e82e9367b7d257e07c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_3
🎮 Level 9: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 9 completion - 100% enemies defeated
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 10: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_1 for: Level 10 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (136ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x3c08609accd291cef3af6597436c3ba3eb8befcdeaf9d0e769e05d5041ef6347
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x3c08609accd291cef3af6597436c3ba3eb8befcdeaf9d0e769e05d5041ef6347
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🚫 Level 10: No reward (level_already_completed_today)
🚫 Level 10: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x16d502f18a384f8e793f4dcc0853b18f876a959e1ff0565b8588eda4674727bd
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_1: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_1, 200 ETH, 0x16d502f18a384f8e793f4dcc0853b18f876a959e1ff0565b8588eda4674727bd
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_1, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_1
🎮 Level 10: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 10 completion - 100% enemies defeated
🚫 Level 10: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (154ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x7a03a9d4132176856658b9a5bab4b9d31b98ae3677ac788a58a40963b3c178f6
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_3: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 200 ETH, 0x7a03a9d4132176856658b9a5bab4b9d31b98ae3677ac788a58a40963b3c178f6
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_3
🎮 Level 10: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 10 completion - 100% enemies defeated
🚫 Level 10: No reward (level_already_completed_today)
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ API Call: POST /wallet/send (135ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x4ba745a922449568fe6c510e80330f7b313fbcce77cb8be2d135dbdf536e298e
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x4ba745a922449568fe6c510e80330f7b313fbcce77cb8be2d135dbdf536e298e
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: 7625.000944 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_1
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_3
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_2
🎯 Starting grinder session: grinder_2
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (145ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x910aa9de8c609d88b7a4d931358e96329311e0ba100318f3d3455c2983b5274f
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0x910aa9de8c609d88b7a4d931358e96329311e0ba100318f3d3455c2983b5274f
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎮 Level 2: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (145ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xcbb899173eafa3193df5f3d002b6b3983cc0ed9eb53b6c710379ed3899a054ac
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xcbb899173eafa3193df5f3d002b6b3983cc0ed9eb53b6c710379ed3899a054ac
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
✅ API Call: POST /generate-environment (8279ms)
🎨 Environment Creation Tracked: Glacial Aurora by creator_2
✅ Environment created by creator_2: Glacial Aurora
✅ Environment created: Glacial Aurora by creator_2
🎮 Level 3: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated
📊 Multi-Account Coordination Results:
   Coordination Duration: 8604ms
   Accounts Coordinated: 5
   Treasury Impact: Analyzing...
✅ API Call: POST /wallet/send (148ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x798bd46910b470c1880277d80d91b817aeaeb791e8b51ea3e60364f7af538d07
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0x798bd46910b470c1880277d80d91b817aeaeb791e8b51ea3e60364f7af538d07
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎮 Level 4: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (160ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xa1d800d3d9e3330806a2707a77becf1ee51c53aa2164b7ce7186ce357470f7fa
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0xa1d800d3d9e3330806a2707a77becf1ee51c53aa2164b7ce7186ce357470f7fa
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎮 Level 5: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated
💰 Balance change: -125.000254 ETH (1749.998950 ETH total)
📉 Treasury outflow detected: -125.000254 ETH
✅ API Call: POST /wallet/send (175ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x0bcbb3a2e1785da666e5de7be162db0e61db62097ab5e628df6e529ba5412b0d
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_2: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 125 ETH, 0x0bcbb3a2e1785da666e5de7be162db0e61db62097ab5e628df6e529ba5412b0d
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_2
🎮 Level 6: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (167ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x90f02feae216b8d03c1d76cf2644c9445706c5edceec2c03288a8ba9f166ae9c
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 6 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x90f02feae216b8d03c1d76cf2644c9445706c5edceec2c03288a8ba9f166ae9c
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🎮 Level 7: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 7 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (150ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x37018ac65f8b338d511d22710fd0cf32dabbcc5c207128c870f05f4a6674d9e0
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 7 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x37018ac65f8b338d511d22710fd0cf32dabbcc5c207128c870f05f4a6674d9e0
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🎮 Level 8: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 8 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (151ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x11aff4aef0036fb0058358a2a988e7add02c1a0362324d999fbfca5ef9e71f1d
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 8 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x11aff4aef0036fb0058358a2a988e7add02c1a0362324d999fbfca5ef9e71f1d
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🎮 Level 9: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 9 completion - 100% enemies defeated
✅ API Call: POST /generate-environment (12624ms)
🎨 Environment Creation Tracked: Ancient Temple Ruins Jungle by creator_1
✅ Environment created by creator_1: Ancient Temple Ruins Jungle
✅ Environment created: Ancient Temple Ruins Jungle by creator_1
✅ Environment created: Ancient Temple Ruins Jungle by creator_1
🎨 creator_2 creating environment: "Desert oasis with sandstorms"
✅ API Call: POST /wallet/send (160ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x528e32b44c3687d61ab611822000a3f99c020fdef89159c0e4271828fae137f7
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 9 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x528e32b44c3687d61ab611822000a3f99c020fdef89159c0e4271828fae137f7
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🎮 Level 10: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_2 for: Level 10 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (150ms)
💰 ETH Transfer Tracked: 200 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 200 ETH to ******************************************
📤 Transaction hash: 0x008f1676ff2842b0082137dabac08d1947043e5f0e0e09c32b84df0a2c480e05
📉 Treasury outflow recorded: -200.000000 ETH (Reward to grinder_2: Level 10 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_2, 200 ETH, 0x008f1676ff2842b0082137dabac08d1947043e5f0e0e09c32b84df0a2c480e05
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_2, amount=200
🎁 Token Award Tracked: 200 tokens to grinder_2
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_2: 9250.001155 ETH
🎯 Starting grinder session: grinder_3
🎮 Level 1: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x562b1b2f57ee96a8c74e383f0687631cd6d6e4d3574e6c8235dfe49fe61fd0a9
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 1 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0x562b1b2f57ee96a8c74e383f0687631cd6d6e4d3574e6c8235dfe49fe61fd0a9
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
💰 Balance change: +124.999874 ETH (624.998824 ETH total)
📈 Treasury inflow detected: +124.999874 ETH
🎮 Level 2: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (147ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xca14bf88b7dcbfd12e836290336e326837f5f789300900bd425794b325f50e59
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 2 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xca14bf88b7dcbfd12e836290336e326837f5f789300900bd425794b325f50e59
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎮 Level 3: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (145ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0x9a89835ac44034d71b71e8bf3223582a7389cf2368872eb6df8f4952b2ad2828
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 3 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0x9a89835ac44034d71b71e8bf3223582a7389cf2368872eb6df8f4952b2ad2828
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎮 Level 4: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (150ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xbd2553c0e742f523f0a63ce4c8aa1467c4b4e27c34cdbc5b9788e29517b3895b
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 4 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xbd2553c0e742f523f0a63ce4c8aa1467c4b4e27c34cdbc5b9788e29517b3895b
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
✅ API Call: POST /generate-environment (15951ms)
🎨 Environment Creation Tracked: Aurora Frost by creator_1
✅ Environment created by creator_1: Aurora Frost
✅ Environment created: Aurora Frost by creator_1
📊 Treasury Drain Test Results:
   Stress Duration: 16945ms
   Users Participating: 10
   Initial Balance: 10000 ETH
   Final Balance: 249.99876093741082 ETH
   Total Drain: 9750.001239 ETH
   Drain Percentage: 97.50%
   Treasury Survived: ✅ YES
🎮 Level 5: 1250 WISH → 125 ETH (90% discount applied)
🎁 Awarding 125 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated
✅ API Call: POST /wallet/send (148ms)
💰 ETH Transfer Tracked: 125 ETH from ****************************************** to ******************************************
✅ REAL ETH transfer completed: 125 ETH to ******************************************
📤 Transaction hash: 0xfbb709cb5ca0ec49c0442cae1ce8c2fcdb791432d2edb79e0844c08b340d34ce
📉 Treasury outflow recorded: -125.000000 ETH (Reward to grinder_3: Level 5 completion - 100% enemies defeated)
🔍 Tracking token award: grinder_3, 125 ETH, 0xfbb709cb5ca0ec49c0442cae1ce8c2fcdb791432d2edb79e0844c08b340d34ce
🔍 TransactionTracker.trackTokenAward called: isTracking=true, userId=grinder_3, amount=125
🎁 Token Award Tracked: 125 tokens to grinder_3
🎮 Level 6: 2000 WISH → 200 ETH (90% discount applied)
🎁 Awarding 200 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated
❌ API Call Failed: POST /wallet/send - Real ETH transfer failed: 500 - {"error":"Failed to send transaction: Failed to send transaction: could not coalesce error (error={ \"code\": -32000, \"data\": { \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, payload={ \"id\": 679, \"jsonrpc\": \"2.0\", \"method\": \"eth_sendRawTransaction\", \"params\": [ \"0x02f875827a6935843b9aca00843bb4d200825209943c44cdddb6a900fa2b585dd299e03d12fa4293bc890ad78ebc5ac620000080c080a08051e432505b30e5f71a62c0f979dbebf9098ebefaf27c998c61fd6a4b236d92a00bee33ac71353e6b8064edde6ad9939275586ea25585ef35a6958e04fe266bd8\" ] }, code=UNKNOWN_ERROR, version=6.15.0)"}
❌ Real ETH transfer failed for grinder_3: Error: Real ETH transfer failed: 500 - {"error":"Failed to send transaction: Failed to send transaction: could not coalesce error (error={ \"code\": -32000, \"data\": { \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, payload={ \"id\": 679, \"jsonrpc\": \"2.0\", \"method\": \"eth_sendRawTransaction\", \"params\": [ \"0x02f875827a6935843b9aca00843bb4d200825209943c44cdddb6a900fa2b585dd299e03d12fa4293bc890ad78ebc5ac620000080c080a08051e432505b30e5f71a62c0f979dbebf9098ebefaf27c998c61fd6a4b236d92a00bee33ac71353e6b8064edde6ad9939275586ea25585ef35a6958e04fe266bd8\" ] }, code=UNKNOWN_ERROR, version=6.15.0)"}
    at UserSimulator.awardTokens (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:447:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async UserSimulator.simulateGrindingSession (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:217:17)
    at async TokenomicsStressTest.runSequentialGrindingTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:340:13)
    at async Promise.all (index 0)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:271:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ Stress test failed: Error: Real ETH transfer failed: 500 - {"error":"Failed to send transaction: Failed to send transaction: could not coalesce error (error={ \"code\": -32000, \"data\": { \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, payload={ \"id\": 679, \"jsonrpc\": \"2.0\", \"method\": \"eth_sendRawTransaction\", \"params\": [ \"0x02f875827a6935843b9aca00843bb4d200825209943c44cdddb6a900fa2b585dd299e03d12fa4293bc890ad78ebc5ac620000080c080a08051e432505b30e5f71a62c0f979dbebf9098ebefaf27c998c61fd6a4b236d92a00bee33ac71353e6b8064edde6ad9939275586ea25585ef35a6958e04fe266bd8\" ] }, code=UNKNOWN_ERROR, version=6.15.0)"}
    at UserSimulator.awardTokens (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:447:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async UserSimulator.simulateGrindingSession (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:217:17)
    at async TokenomicsStressTest.runSequentialGrindingTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:340:13)
    at async Promise.all (index 0)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:271:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ Stress test failed: Error: Real ETH transfer failed: 500 - {"error":"Failed to send transaction: Failed to send transaction: could not coalesce error (error={ \"code\": -32000, \"data\": { \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, \"message\": \"Sender doesn't have enough funds to send tx. The max upfront cost is: 200000021036827369984 and the sender's balance is: 124998739919497991000.\" }, payload={ \"id\": 679, \"jsonrpc\": \"2.0\", \"method\": \"eth_sendRawTransaction\", \"params\": [ \"0x02f875827a6935843b9aca00843bb4d200825209943c44cdddb6a900fa2b585dd299e03d12fa4293bc890ad78ebc5ac620000080c080a08051e432505b30e5f71a62c0f979dbebf9098ebefaf27c998c61fd6a4b236d92a00bee33ac71353e6b8064edde6ad9939275586ea25585ef35a6958e04fe266bd8\" ] }, code=UNKNOWN_ERROR, version=6.15.0)"}
    at UserSimulator.awardTokens (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:447:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async UserSimulator.simulateGrindingSession (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:217:17)
    at async TokenomicsStressTest.runSequentialGrindingTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:340:13)
    at async Promise.all (index 0)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:271:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)