# WarpSector - Reality Warping Shooter - Codebase Map

## Overview
WarpSector is a vertical scrolling shooter game with AI-powered reality warping mechanics, built with HTML5 Canvas and JavaScript. The game features a sophisticated token economy system, AI-generated environments, and integration with blockchain technology through Orange ID authentication.

## Architecture Overview

### Core Architecture Pattern
The codebase follows a **modular component-based architecture** with clear separation of concerns:

- **GameEngine**: Central orchestrator managing game loop and state
- **Managers**: Handle specific subsystems (Auth, Tokens, AI, Enemies, etc.)
- **Systems**: Provide foundational services (Input, Rendering, Audio, etc.)
- **Entities**: Game objects with behavior and state (Player, Enemy, Boss, etc.)
- **Utils**: Shared utilities and math functions

### Technology Stack
- **Frontend**: Vanilla JavaScript ES6+ modules, HTML5 Canvas
- **Backend**: Node.js Express server with AI integration
- **Build Tool**: Vite for development and production builds
- **AI Services**: Fal.ai for image generation, Groq LLM for environment analysis
- **Authentication**: Orange ID (Bedrock Passport) with debug bypass
- **Blockchain**: Ethereum integration with Chain ID 31337 (Hardhat local development)

## Project Structure

```
WarpSector/
├── src/                          # Frontend source code
│   ├── core/                     # Core game engine
│   │   ├── GameEngine.js         # Main game loop and state management
│   │   └── AudioManager.js       # Audio system management
│   ├── entities/                 # Game entities
│   │   ├── PlayerShip.js         # Player ship with movement, weapons, health
│   │   ├── Enemy.js              # Base enemy class with type-specific behaviors
│   │   ├── Boss.js               # Boss enemies with special abilities
│   │   ├── Projectile.js         # Player and enemy projectiles
│   │   └── PowerUpCollectible.js # Power-up items
│   ├── managers/                 # Game subsystem managers
│   │   ├── RealityWarpManager.js # AI-powered environment transformation
│   │   ├── TokenEconomyManager.js# WISH token economy and wallet integration
│   │   ├── EnemyManager.js       # Enemy spawning and wave management
│   │   ├── LevelManager.js       # Level progression and configuration
│   │   ├── BossWarpManager.js    # Boss encounter and warp mechanics
│   │   ├── OrangeSDKManager.js   # Orange SDK integration for tournaments
│   │   ├── HangarManager.js      # Ship upgrade system
│   │   └── AuthManager.js        # Authentication via Orange ID
│   ├── systems/                  # Game systems
│   │   ├── WeaponSystem.js       # Player weapon management
│   │   ├── EnemyProjectileSystem.js # Enemy firing patterns
│   │   ├── PowerUp.js            # Power-up effects and management
│   │   └── Consumable.js         # Consumable item system
│   ├── ui/                       # User interface components
│   │   ├── MainMenu.js           # Main menu with authentication
│   │   ├── GenieInterface.js     # Shop interface for purchases
│   │   ├── PowerUpIndicator.js   # Power-up status display
│   │   └── BoostGauge.js         # Boost meter UI
│   ├── vfx/                      # Visual effects
│   │   ├── BloomEffect.js        # Real-time bloom post-processing
│   │   └── WarpVFXSystem.js      # Reality warp visual effects
│   ├── config/                   # Configuration files
│   │   ├── gameConfig.js         # Game constants and settings
│   │   └── audioConfig.js        # Audio file configurations
│   ├── utils/                    # Utility classes
│   │   ├── GameObject.js         # Base game object class
│   │   ├── Vector2.js            # 2D vector mathematics
│   │   ├── GameMath.js           # Game-specific math functions
│   │   └── ObjectPool.js         # Object pooling for performance
│   └── main.js                   # Application entry point
├── server/                       # Backend API server
│   ├── index.js                  # Express server with AI endpoints
│   ├── ai/                       # AI service integration
│   │   ├── EnvironmentService.js # Client-side environment service
│   │   ├── FalaiClient.js        # Fal.ai image generation client
│   │   └── LLMClient.js          # Groq LLM integration
│   ├── wallet/                   # Blockchain integration
│   │   └── WalletManager.js      # Hot wallet management
│   └── middleware/               # Authentication middleware
├── assets/                       # Game assets (sprites, audio)
├── test/                         # Test files and frameworks
└── coverage/                     # Test coverage reports
```

## Core Systems Deep Dive

### 1. GameEngine (src/core/GameEngine.js)
**Purpose**: Central game loop orchestrator with fixed timestep architecture
**Key Responsibilities**:
- **Fixed Timestep Loop**: 60 FPS target with accumulator pattern for consistent physics
- **State Management**: Handles game states (MENU, GAME_PLAY, PAUSED, GAME_OVER, LEVEL_COMPLETE)
- **System Initialization**: Coordinates initialization of all managers and systems
- **Rendering Pipeline**: Manages bloom effects, VFX systems, and layered rendering
- **Collision Detection**: Centralized collision handling between all game objects
- **Environment Management**: Applies environmental effects to gameplay

**Key Methods**:
- `gameLoop()`: Main game loop with fixed timestep
- `update()`: Updates all game systems with delta time
- `render()`: Renders game state with interpolation
- `startGameplay()`: Transitions from menu to active gameplay
- `changeEnvironment()`: Applies new environment with visual effects

### 2. Reality Warp System (src/managers/RealityWarpManager.js)
**Purpose**: AI-powered environment transformation system
**Key Features**:
- **Fixed Cost Model**: 25,000 WISH tokens per warp (no discounts)
- **Backend AI Integration**: Uses server-side LLM and image generation
- **Environment Effects**: Modifies enemy stats, spawn rates, and behaviors
- **Session Limits**: Maximum 3 warps per session to prevent abuse
- **Visual Effects**: Triggers warp VFX and bloom enhancements

**Workflow**:
1. User provides environment description via Genie Interface
2. Server generates image prompt and gameplay modifiers using LLM
3. Fal.ai generates background image based on prompt
4. Game applies environmental modifiers to enemies
5. Visual effects system renders warp transition

### 3. Token Economy System (src/managers/TokenEconomyManager.js)
**Purpose**: WISH token management with blockchain integration
**Key Components**:
- **Wallet Integration**: MetaMask/OrangeID with Chain ID 31337 enforcement IN ETH TEST MODE, FOR TESTING ON LOCAL ETH BLOCKCHAIN. 
- **Performance-Based Rewards**: Tokens awarded based on level completion, speed, accuracy
- **Transaction History**: Complete audit trail of all token movements
- **Hot Wallet System**: Server-side wallet for automated transactions
- **Daily Rewards**: Time-gated reward system to encourage daily play

**Reward Calculation**:
```
Base Reward = 1250 * level_number
Speed Bonus = Base * (1.0 + speed_multiplier)
Accuracy Bonus = Base * (1.0 + accuracy_multiplier)
Perfect Bonus = Base * 1.25
Final Reward = Base * Speed * Accuracy * Perfect * Difficulty
```

### 4. Enemy Management System (src/managers/EnemyManager.js)
**Purpose**: Space Invaders-style enemy wave management
**Key Features**:
- **Grid-Based Formations**: Enemies spawn in organized patterns
- **Environmental Compatibility**: Enemy types have strengths/weaknesses per environment
- **Dive Attack System**: Enemies break formation to attack player
- **Boss Encounters**: Special boss fights every 8 waves
- **Scaling Difficulty**: Enemy stats increase with wave number

**Enemy Types & Rock-Paper-Scissors Mechanics**:
- **Water**: Strong vs Fire, weak vs Earth
- **Fire**: Strong vs Air, weak vs Water  
- **Air**: Strong vs Earth, weak vs Fire
- **Earth**: Strong vs Water, weak vs Air
- **Crystal**: Resistant to most, vulnerable to focused attacks
- **Shadow**: Fast and evasive, vulnerable to light environments

### 5. Authentication System (src/auth/AuthManager.js)
**Purpose**: Orange ID integration with debug bypass
**Key Features**:
- **Orange ID Widget**: Embedded React-based authentication
- **Debug Mode**: Bypass authentication for development
- **Wallet Integration**: Links Orange ID to Ethereum wallet
- **Session Management**: Handles login/logout with proper cleanup

## Backend Architecture (server/)

### AI Service Integration
The backend provides AI-powered services through Express endpoints:

**Environment Generation Endpoint** (`/api/generate-environment`):
1. Receives user environment description
2. Uses Groq LLM to generate image prompt and gameplay modifiers
3. Calls Fal.ai to generate background image
4. Returns complete environment data with image URL and modifiers

**Image Generation** (`/api/falai/generate-image`):
- Direct Fal.ai integration for custom image generation
- Configurable parameters (size, steps, safety filters)
- Returns generated image data

**LLM Completion** (`/api/groq/generate-completion`):
- Groq LLM integration for text generation
- Configurable model parameters (temperature, max tokens)
- Used for environment analysis and prompt generation

### Wallet Management System
**Hot Wallet Operations**:
- **Address**: `******************************************`
- **Token Transactions**: Award/spend WISH tokens
- **ETH Transfers**: Send ETH for reward distribution
- **Balance Queries**: Real-time wallet balance checking

**Security Measures**:
- Development-only authentication middleware
- Chain ID enforcement (31337 only)
- Transaction validation and error handling

## Game Flow Architecture

### 1. Initialization Flow
```
main.js → Game.initialize() → GameEngine.init() → InitializeSystems()
├── AuthManager.initializeOrangeID()
├── AudioManager.loadAudioFiles()
├── EnemyManager.loadEnemySprites()
├── RealityWarpManager.init()
├── TokenEconomyManager.initializeWallet()
└── OrangeSDKManager.initialize()
```

### 2. Game Start Flow
```
MainMenu.startGame() → GameEngine.startGameplay()
├── Show game canvas
├── Reset player health/lives
├── Start background music
├── LevelManager.startLevel(1)
└── Show power-up indicators
```

### 3. Reality Warp Flow
```
GenieInterface → RealityWarpManager.executeWarp()
├── Validate token balance (25,000 WISH)
├── Deduct tokens from wallet
├── Send environment description to server
├── Server: LLM generates prompt + modifiers
├── Server: Fal.ai generates background image
├── Apply environmental effects to enemies
├── Update visual effects (bloom, warp VFX)
└── Return success/failure status
```

### 4. Level Completion Flow
```
EnemyManager.checkWaveCompletion() → TokenEconomyManager.handleLevelCompletion()
├── Calculate performance-based rewards
├── Apply speed/accuracy/perfect bonuses
├── Sync rewards to wallet (if connected)
├── Update daily reward tracker
├── Show level completion overlay
└── Prepare next level environment
```

## Visual Effects Systems

### Bloom Effect System (src/vfx/BloomEffect.js)
- **Real-time Post-processing**: Applied after main rendering
- **Configurable Parameters**: Intensity, threshold, blur radius, downsample
- **Performance Optimized**: Automatic frame skipping for low-end devices
- **Debug Controls**: Hotkeys for real-time adjustment (B, +/-, [/])

### Warp VFX System (src/vfx/WarpVFXSystem.js)
- **Reality Warp Transitions**: Visual effects during environment changes
- **Particle Systems**: Custom particle effects for warp events
- **Screen Distortion**: Visual distortion effects during transitions
- **Performance Monitoring**: Frame rate aware effect scaling

## Audio System Architecture

### AudioManager (src/core/AudioManager.js)
- **Multi-track System**: Separate tracks for music, SFX, ambient sounds
- **Volume Control**: Individual volume controls per track type
- **Context Management**: Handles Web Audio API context activation
- **Sound Pooling**: Efficient sound effect playback with object pooling

### Audio Configuration (src/config/audioConfig.js)
- **File Mapping**: Maps sound types to audio file paths
- **Volume Presets**: Default volume levels for different sound categories
- **Loop Settings**: Configurable looping for background music

## Performance Optimization Strategies

### 1. Object Pooling
- **Enemy Pooling**: Reuse enemy objects to avoid garbage collection
- **Projectile Pooling**: Efficient projectile lifecycle management
- **Effect Pooling**: Reuse visual effect objects

### 2. Rendering Optimizations
- **Fixed Timestep**: Consistent 60 FPS with interpolation
- **Layered Rendering**: Separate layers for background, entities, effects, UI
- **Bloom Downsampling**: Reduced resolution for bloom effect calculations
- **Collision Grid**: Spatial partitioning for efficient collision detection

### 3. AI Service Caching
- **Default Environment Caching**: Pre-generated default environment stored server-side
- **Image Caching**: Generated images saved locally to avoid re-generation
- **Fallback Systems**: Graceful degradation when AI services are unavailable

## Security & Authentication

### Orange ID Integration
- **Tenant Configuration**: Pre-configured Orange ID tenant and API keys
- **Debug Mode**: Development bypass for authentication
- **Wallet Linking**: Automatic wallet address association with Orange ID

### Blockchain Security
- **Chain ID Enforcement**: Hard-coded to Chain ID 31337 (Hardhat local)
- **Mainnet Blocking**: Prevents accidental mainnet transactions
- **Transaction Validation**: Comprehensive input validation for all blockchain operations

## Testing & Development

### Test Framework
- **Jest Configuration**: ES6 module support with jsdom environment
- **Unit Tests**: Individual component testing for managers and systems
- **Integration Tests**: End-to-end testing of game flows
- **Coverage Reports**: Comprehensive test coverage tracking

### Development Tools
- **Hot Module Replacement**: Vite development server with HMR
- **Debug Controls**: In-game debug menus and hotkeys
- **Logging System**: Configurable console logging with performance metrics
- **Error Handling**: Comprehensive error catching and user feedback

## Deployment Architecture

### Development Mode
```bash
npm run dev:all  # Starts both frontend and backend concurrently
```

### Production Build
```bash
npm run build    # Creates optimized production build
npm run preview  # Serves production build locally
```

### Server Deployment
```bash
npm run server   # Starts production backend server
```

## Key Integration Points

### 1. Frontend-Backend Communication
- **API Base URL**: `http://localhost:3001/api`
- **Health Check**: `/api/health` endpoint for service availability
- **Environment Generation**: `/api/generate-environment` for AI-powered environments
- **Wallet Operations**: `/api/wallet/*` endpoints for blockchain interactions

### 2. AI Service Integration
- **Fal.ai**: Image generation for custom environments
- **Groq LLM**: Environment analysis and prompt generation
- **Fallback Systems**: Default environments when AI services unavailable

### 3. External Service Dependencies
- **Orange ID**: Authentication and user management
- **MetaMask/Wallet**: Blockchain transaction signing
- **Hardhat Local**: Local Ethereum development network

This codebase represents a sophisticated integration of traditional game development patterns with modern web technologies, AI services, and blockchain integration, creating a unique gaming experience that combines classic arcade gameplay with cutting-edge features.