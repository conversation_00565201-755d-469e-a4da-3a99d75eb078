/**
 * Game configuration constants
 */
export const GAME_CONFIG = {
    // Canvas settings
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    TARGET_FPS: 60,
    
    // Game mechanics
    PLAYER_LIVES: 3,
    PLAYER_HEALTH: 100,
    
    // Token economy
    BASE_LEVEL_REWARD: 1250,
    POWER_UP_COSTS: {
        EXTRA_WINGMAN: 10000,
        EXTRA_LIFE: 15000,
        SPREAD_AMMO: 7500,
        REALITY_WARP: 25000
    },

    // Reward scaling system - ensures house edge while allowing big wins
    REWARD_SYSTEM: {
        BASE_MULTIPLIER: 1.0,           // Base reward multiplier
        POWER_UP_BONUS_MULTIPLIER: 0.5, // Each power-up adds 50% to potential rewards
        MAX_BONUS_MULTIPLIER: 3.0,      // Maximum total multiplier (300% of base)
        CHANCE_REDUCTION_FACTOR: 0.8,   // Higher multipliers = lower success chance
        HOUSE_EDGE: 0.15,               // 15% house edge on expected value
        MIN_REWARD_CHANCE: 0.3,         // Minimum 30% chance of getting some reward
        JACKPOT_CHANCE: 0.05,           // 5% chance of jackpot (2x multiplier)
        BUST_CHANCE_BASE: 0.1           // Base 10% chance of getting nothing
    },

    // Reality warp costs
    WARP_BASE_COST: 20000,

    // Raffle system
    RAFFLE_PRIZE_POOL_PERCENTAGE: 0.5,
    RAFFLE_PRIZES: {
        GOLD: 25000,
        SILVER: 15000,
        BRONZE: 10000
    },
    
    // Development settings
    DEBUG_MODE: true,
    ENABLE_CONSOLE_LOGS: true
};

export const ENVIRONMENT_TYPES = {
    SPACE: 'space',
    WATER: 'water',
    FIRE: 'fire',
    AIR: 'air',
    EARTH: 'earth',
    CRYSTAL: 'crystal',
    SHADOW: 'shadow'
};

export const ENEMY_TYPES = {
    WATER: 'water',
    FIRE: 'fire',
    AIR: 'air',
    EARTH: 'earth',
    CRYSTAL: 'crystal',
    SHADOW: 'shadow'
};