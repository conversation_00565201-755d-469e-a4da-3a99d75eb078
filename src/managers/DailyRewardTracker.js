import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * DailyRewardTracker handles the daily reward limitations and tracking
 * Implements the new tokenomics system with daily max rewards per level
 */
export class DailyRewardTracker {
    constructor(userId = 'default') {
        this.userId = userId;

        // Daily tracking data - now per-user
        this.dailyData = {
            date: this.getCurrentDate(),
            completedLevels: new Set(), // Levels completed today by this user
            levelCompletionRates: {}, // Completion rates per level for this user
            dailyTokensEarned: 0,
            maxDailyTokens: 10000, // Reduced from 100,000 to 10,000 for sustainability
            sessionTokensEarned: 0, // Track tokens earned in current session
            sessionStartTime: Date.now(),
            levelCompletionCount: {} // Track how many times each level was attempted today
        };
        
        // Load saved data from localStorage
        this.loadFromStorage();
        
        // Ensure we're tracking the current day
        this.ensureCurrentDate();
    }
    
    /**
     * Get current date in YYYY-MM-DD format
     * @returns {string} Current date string
     */
    getCurrentDate() {
        return new Date().toISOString().split('T')[0];
    }
    
    /**
     * Ensure we're tracking data for the current date
     */
    ensureCurrentDate() {
        const today = this.getCurrentDate();
        if (this.dailyData.date !== today) {
            // Reset daily data for new day
            this.dailyData = {
                date: today,
                completedLevels: new Set(),
                levelCompletionRates: {},
                dailyTokensEarned: 0,
                maxDailyTokens: 100000
            };
            this.saveToStorage();
        }
    }
    
    /**
     * Check if a level can be rewarded today (not already completed)
     * @param {number} levelNumber - Level number to check
     * @returns {boolean} True if level can be rewarded
     */
    canEarnReward(levelNumber) {
        this.ensureCurrentDate();
        return !this.dailyData.completedLevels.has(levelNumber);
    }
    
    /**
     * Record level completion and reward
     * @param {number} levelNumber - Level number completed
     * @param {number} completionRate - Completion rate (0.0 - 1.0)
     * @param {number} rewardAmount - Token reward amount
     */
    recordLevelCompletion(levelNumber, completionRate, rewardAmount) {
        this.ensureCurrentDate();
        
        // Mark level as completed today
        this.dailyData.completedLevels.add(levelNumber);
        
        // Store completion rate
        this.dailyData.levelCompletionRates[levelNumber] = completionRate;
        
        // Add to daily tokens earned
        this.dailyData.dailyTokensEarned += rewardAmount;
        
        // Save to localStorage
        this.saveToStorage();
    }
    
    /**
     * Calculate max reward for a level based on exponential scaling
     * @param {number} levelNumber - Level number
     * @returns {number} Max reward amount
     */
    calculateMaxReward(levelNumber) {
        const baseReward = GAME_CONFIG.BASE_LEVEL_REWARD; // 1250 from gameConfig
        const exponentialMultiplier = Math.pow(1.6, Math.floor((levelNumber - 1) / 5));
        return Math.floor(baseReward * exponentialMultiplier);
    }
    
    /**
     * Calculate reward based on completion percentage (quadratic scaling)
     * @param {number} levelNumber - Level number
     * @param {object} levelData - Level data with totalEnemies and enemiesDefeated
     * @returns {number} Reward amount
     */
    calculateCompletionReward(levelNumber, levelData) {
        const { totalEnemies, enemiesDefeated } = levelData;
        const completionPercentage = totalEnemies > 0 ? enemiesDefeated / totalEnemies : 0;
        
        // Linear scaling - 50% completion = 50% reward, 100% = 100% reward
        const rewardPercentage = completionPercentage;
        
        // Get max reward for this level
        const maxReward = this.calculateMaxReward(levelNumber);
        
        return Math.floor(maxReward * rewardPercentage);
    }
    
    /**
     * Check if daily token limit has been reached
     * @returns {boolean} True if daily limit reached
     */
    isDailyLimitReached() {
        this.ensureCurrentDate();
        return this.dailyData.dailyTokensEarned >= this.dailyData.maxDailyTokens;
    }
    
    /**
     * Get remaining daily token earning capacity
     * @returns {number} Remaining tokens that can be earned today
     */
    getRemainingDailyCapacity() {
        this.ensureCurrentDate();
        return Math.max(0, this.dailyData.maxDailyTokens - this.dailyData.dailyTokensEarned);
    }
    
    /**
     * Get daily progress data
     * @returns {object} Daily progress data
     */
    getDailyProgress() {
        this.ensureCurrentDate();
        return {
            date: this.dailyData.date,
            completedLevels: Array.from(this.dailyData.completedLevels),
            levelCompletionRates: { ...this.dailyData.levelCompletionRates },
            dailyTokensEarned: this.dailyData.dailyTokensEarned,
            maxDailyTokens: this.dailyData.maxDailyTokens,
            remainingCapacity: this.getRemainingDailyCapacity(),
            isLimitReached: this.isDailyLimitReached()
        };
    }
    
    /**
     * Load data from localStorage
     */
    loadFromStorage() {
        try {
            const storageKey = `dailyRewardTracker_${this.userId}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                this.dailyData = {
                    date: parsed.date || this.getCurrentDate(),
                    completedLevels: new Set(parsed.completedLevels || []),
                    levelCompletionRates: parsed.levelCompletionRates || {},
                    dailyTokensEarned: parsed.dailyTokensEarned || 0,
                    maxDailyTokens: parsed.maxDailyTokens || 100000
                };
            }
        } catch (error) {
            console.warn('Failed to load daily reward data from localStorage:', error);
        }
    }
    
    /**
     * Save data to localStorage
     */
    saveToStorage() {
        try {
            const dataToSave = {
                date: this.dailyData.date,
                completedLevels: Array.from(this.dailyData.completedLevels),
                levelCompletionRates: this.dailyData.levelCompletionRates,
                dailyTokensEarned: this.dailyData.dailyTokensEarned,
                maxDailyTokens: this.dailyData.maxDailyTokens
            };
            const storageKey = `dailyRewardTracker_${this.userId}`;
            localStorage.setItem(storageKey, JSON.stringify(dataToSave));
        } catch (error) {
            console.warn('Failed to save daily reward data to localStorage:', error);
        }
    }
    
    /**
     * Reset daily data (for testing)
     */
    reset() {
        this.dailyData = {
            date: this.getCurrentDate(),
            completedLevels: new Set(),
            levelCompletionRates: {},
            dailyTokensEarned: 0,
            maxDailyTokens: 100000
        };
        this.saveToStorage();
    }
}